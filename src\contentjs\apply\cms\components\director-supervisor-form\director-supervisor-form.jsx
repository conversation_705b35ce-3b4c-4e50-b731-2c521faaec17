/* eslint-disable react/no-unescaped-entities */
/***************************************************
 * 說明: 路由元件 - 申請書: 董事/監察人
 * Copyright(c) 2024 AEB Inc. All Rights Reserved.
 ***************************************************/
import React from 'react';
import { withErrorBoundary } from 'react-error-boundary';
import { useNavigate } from 'react-router-dom';
import { useTranslation, Trans } from 'react-i18next';

import Input from 'components/InputField';
import Button from 'components/Button';
import AddressDialog from 'components/InputField/AddressDialogField';
import { awaitOpenDialog } from 'components/Dialog';
import CheckBox from 'components/InputField/CheckboxField';
import { Tooltip } from '@mui/material';
import RemarkText from 'components/RemarkText';

import { LANG } from 'js/i18n-util';
import { ApplyType, ShareHolderModifyCodes } from 'js/constants';
import { processCheckboxChange, containsOne } from 'js/common-util';

import CommonButton from 'contentjs/apply/components/common-button';
import FormCommonButton from 'contentjs/form/template/components/common-button';

import * as actions from './actions';
const { Fragment, useEffect, useState } = React;

const __group = 'director-supervisor-form';

const Component = ({ store, dispatch }) => {
    
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const { directorSupervisorForm, ifOss, ifCfm, isViewMode, caseCodeList, applyType } = store;
    const { isApply, isCmpyLtd, is102, is401, is402, is414, is418, is604, is403, dtrAmt, spvrAmt, shPosCodeMap, orgType, isDeletable } = directorSupervisorForm;
    const handleChange = (name, value) => {
        dispatch(actions.setStateData(`validateResult.${name}`, null));   
        dispatch(actions.setStateData(name, value));
    };

    const [ ifEn, setIfEn ] = useState();
    /** 語言 */
    useEffect(() => {
        // 為英文時的處理
        setIfEn(i18n.language === LANG.EN);
    }, [ i18n.language ]);

    const [ifRegChange, setIfRegChange] = useState(false); // 是否為"變更登記"
    useEffect(() => {
        setIfRegChange(applyType == ApplyType.change || applyType == ApplyType.cert);
    }, [directorSupervisorForm]);

    const [capAmt, setCapAmt] = useState(directorSupervisorForm.capAmt);
    useEffect(() => {
        const totalInvestAmt = (directorSupervisorForm.shareholders || []).reduce((sum, shareholder) => {
            return sum + (shareholder.investAmt || 0) * directorSupervisorForm.shareVal;
        }, 0);

        const updatedRemainingAmount = directorSupervisorForm.orgCapitalAmt - totalInvestAmt;
        setCapAmt(updatedRemainingAmount);
    }, [directorSupervisorForm.shareholders, directorSupervisorForm.capAmt]);

    let dtrAmtCount = 0;
    let totalShCount = 0;
    let curShCount = 0;
    let curSpvrCount = 0;
    let spvrAmtCount = 0;
    let totalSpvrCount = 0;
    const countPeopleAmtLimit = () => {
        dtrAmtCount = (dtrAmt || '').indexOf('人') != -1 ? (dtrAmt.substring(0, dtrAmt.lastIndexOf('人'))) : dtrAmt;
        totalShCount = parseInt(dtrAmtCount);

        if (totalShCount != 0) {
            totalShCount = !totalShCount ? curShCount + 1 : totalShCount;
        }
        
        // 有限公司董事人數至多三人
        if (!is102 && orgType === '02' && totalShCount > 3) {
            // 定為3人
            totalShCount = 3;
        }

        // 監察人數限制
        // 有限公司不會有監察人
        // 有限公司變更組織可增加監察人
        if (is102 || orgType !== '02') {
            // spvrAmtCount = spvrAmt.indexOf('人') != -1 ? spvrAmt.substring(0, spvrAmt.lastIndexOf('人')) : spvrAmt;
            spvrAmtCount = (spvrAmt && spvrAmt.indexOf('人') !== -1) ? spvrAmt.substring(0, spvrAmt.lastIndexOf('人')) : (spvrAmt);
            totalSpvrCount = parseInt(spvrAmtCount);
            if (totalSpvrCount != 0) {
                totalSpvrCount = !totalSpvrCount ? curSpvrCount + 1 : totalSpvrCount;
            }
        }
    };

    // const save = () => {
    //     reFillShList();
    //     dispatch(actions.save());
    // };

    const reFillShList = () => {
        curShCount = 0;
        curSpvrCount = 0;
        let shs = directorSupervisorForm.shareholders;
        for (var i = 0; i < shs.length; i++) {
            if (shs[i].shPosCode === '01') {
                shs[i].shCmpyRep = 'Y';
            } else {
                shs[i].shCmpyRep = 'N';
            }
            if (['01', '02', '03', '04', '06'].indexOf(shs[i].shPosCode) != -1) {
                curShCount++;
            }
            if (['05'].indexOf(shs[i].shPosCode) != -1) {
                curSpvrCount++;
            }
        }
        // 有限公司若無董事長，則三個董事都是公司代表
        if (orgType === '02' && shs.length > 0) {
            if (!(shs[0].shPosCode === '01')) { // 董事長為shList中第一位
                for (var k = 0; k < shs.length; k++) {
                    if (shs[k].shPosCode === '04') {
                        shs[k].shCmpyRep = 'Y'; // 所有最多三位董事皆為公司代表
                    }
                }
            }
        }
    };

    // table新增
    const tableAdd = (shPosCode) => {
        var shCmpyRep;
        reFillShList();
        // 計算董事人數與監察人數限制
        countPeopleAmtLimit();
        // 只檢查"有限公司"
        if (orgType === '02') {
            if (['01', '02', '03', '04', '06'].indexOf(shPosCode) != -1) {
                if (totalShCount >= 0 && curShCount >= totalShCount) {
                    dispatch(awaitOpenDialog({ title: <Trans i18nKey={`${__group}.alert.title`}>錯誤訊息</Trans>,content: (<span><Trans i18nKey={`${__group}.alert.1`}>董事人數已達上限，無法再新增一名「</Trans>{shPosCodeMap[shPosCode]}<Trans i18nKey={`${__group}.alert.2`}>」。如欲增加，請修改董事人數</Trans></span>)}));
                    return;
                }
            } else if (['05'].indexOf(shPosCode) != -1) {
                if (totalSpvrCount >=0 && curSpvrCount >= totalSpvrCount) {
                    dispatch(awaitOpenDialog({ content: <Trans i18nKey={`${__group}.alert.3`}>監察人數已達上限，無法再新增一名「監察人」。如欲增加，請修改監察人人數</Trans>}));
                    return;
                }
            }

        }

        if (shPosCode === '01') {
            if (directorSupervisorForm.shareholders.length > 0 && directorSupervisorForm.shareholders[0].shPosCode === '01') {
                dispatch(awaitOpenDialog({ content: <Trans i18nKey={`${__group}.alert.4`}>僅能有一位董事長</Trans>}));
                return;
            }
            shCmpyRep = 'Y';
        } else {
            shCmpyRep = 'N';
        }

        var originList = directorSupervisorForm.shareholders || [];
        const emptySh = {
            'telixNo': store.telixNo,
            'seqNo': (originList.length + 1),
            'shPosCode': shPosCode,
            'posName': ifCfm ? shPosCodeSelectCfm[shPosCode] : shPosCodeMap[shPosCode], 
            'name': '', 
            'idNo': '', 
            'investAmt': '0',
            'shCmpyRep': shCmpyRep,
            'shCorp': 'N', 
            'shZipCode': '', 
            'shAreaCode': '', 
            'shNeiborCode': '', 
            'shAddr': ''
        };
        originList.push(emptySh);
        const sortedList = originList
            .sort((a, b) => Number(a.shPosCode) - Number(b.shPosCode))
            .map((item, index) => ({
                ...item,
                seqNo: index + 1 // seqNo 從 1 開始
            }));
        dispatch(actions.setStateData('directorSupervisorForm.shareholders', sortedList));
    };

    // table刪除
    const tableDelete = (idx) =>{
        var originList = directorSupervisorForm.shareholders || [];
        var newList = [];
        for (var i = 0 ; i < originList.length ; i++){
            if(i !== idx){
                newList.push(originList[i]);
            }
        }
        dispatch(actions.setStateData('directorSupervisorForm.shareholders', newList));
    };

    // const shPosCodeSelect1 = {
    //     '01': '董事長',
    //     '04': '董事',
    //     '14': '股東',
    // };

    // const shPosCodeSelect2 = {
    //     '14': '股東',
    // };

    const shPosCodeSelect3 = {
        '01': '董事長',
    };

    const shPosCodeSelect4 = {
        '02': '副董事長',
        '03': '常務董事',
        '04': '董事',
        '05': '監察人',
        '06': '獨立董事',
    };

    // const shPosCodeSelect5 = {
    //     '02': '副董事長',
    //     '03': '常務董事',
    // };

    // const shPosCodeSelect6 = {
    //     '15': '重整監督人',
    //     '16': '重整人',
    // };

    const ynMap = {
        'Y': '是',
        'N': '否',
    };

    const shPosCodeSelectCfm = {
        ...shPosCodeSelect3,
        ...shPosCodeSelect4
    };

    // const shPosCodeSelect1En = {
    //     '01': 'Chairman',
    //     '04': 'Director',
    //     '14': 'Shareholder',
    // };
    
    // const shPosCodeSelect2En = {
    //     '14': 'Shareholder',
    // };
    
    // const shPosCodeSelect3En = {
    //     '01': 'Chairman',
    // };
    
    // const shPosCodeSelect4En = {
    //     '02': 'Vice Chairman',
    //     '03': 'Executive Director',
    //     '04': 'Director',
    //     '05': 'Supervisor',
    //     '06': 'Independent Director',
    // };
    
    // const shPosCodeSelect5En = {
    //     '02': 'Vice Chairman',
    //     '03': 'Executive Director',
    // };
    
    // const shPosCodeSelect6En = {
    //     '15': 'Reorganization Supervisor',
    //     '16': 'Reorganizer',
    // };
    
    const ynMapEn = {
        'Y': 'Yes',
        'N': 'No',
    };
    
    // TODO
    const [ isShowDel, setIsShowDel ] = useState(false);
    useEffect(() => {
        // for (let i = 0; i < directorSupervisorForm.shareholders.length; i++) {
        //     const sh = directorSupervisorForm.shareholders[i];
        //     if ((sh.shPosCode === '14') || isApply || isDeletable) {
        //         tableDelete(i);
        //     } else if (sh.shPosCode === '01') {
        //         if (is403 || is414) {
        //             tableDelete(i);
        //         } else if (is401 && orgType == '02') {
        //             tableDelete(i);
        //         } else {
        //             sh.isShowDel = true;
        //         }
        //     } else if (sh.shPosCode === '02') {
        //         if (is401 || is418) {
        //             tableDelete(i);
        //         } else {
        //             sh.isShowDel = true;
        //         }
        //     }
        // }
        // if (directorSupervisorForm.isModifySh || containsOne(ShareHolderModifyCodes, caseCodeList)) {
        //     setIsShowDel(true);
        // }
        setIsShowDel(true);
    }, [directorSupervisorForm.isModifySh, directorSupervisorForm.shareholders, caseCodeList]);

    /** 初始化 */
    useEffect(() => {
        dispatch(actions.init(store, dispatch));
    }, []);

    return (
        <Fragment>
            <div className="form-panel">
                <div className="row g-3">
                    {/* {ifOss && (<> */}
                    {
                        capAmt > 0 && (
                            <div className="col-md-12">
                                <div className="input-group">
                                    <span className="lable_asx"><Trans i18nKey={`${__group}.requiredAmount`}>所需出資額尚餘</Trans>:<br /></span>
                                    <span className="lable_asx">
                                        {capAmt}
                                        <Trans i18nKey={`${__group}.dollar`}></Trans>
                                    </span>
                                </div>
                            </div>
                        )
                    }
                    <div className="page-wrap table_scroll">
                        <table className="tb_rwd">
                            <thead>
                                <tr>
                                    <th colSpan={6}>
                                        <div className="row g-3">
                                            <div className="col-md-3">
                                                <div className="input-group">
                                                    {
                                                        directorSupervisorForm.isCmpyLtd ?
                                                            <Trans i18nKey={`${__group}.title.1`}>董事長、董事、股東名單</Trans>
                                                            :
                                                            <Trans i18nKey={`${__group}.title.2`}>董事長、董事、監察人名單</Trans>
                                                    }
                                                </div>
                                            </div>
                                            <div className="col-md-3">
                                                <div className="input-group">
                                                    {
                                                        ifCfm ? <>
                                                            <Input type="select" value={directorSupervisorForm.shPosCode}
                                                                onChange={(e) => handleChange('directorSupervisorForm.shPosCode', e.target.value)}
                                                                options={shPosCodeSelectCfm}
                                                            />
                                                            <div className="mb-3">
                                                                <Button primary onClick={() => tableAdd(directorSupervisorForm.shPosCode || '01')}>
                                                                    <Trans i18nKey={`${__group}.btn.add`}>增加</Trans>
                                                                </Button>
                                                            </div>
                                                        </> : <>{
                                                            isApply ? <>
                                                                <Input type="select" value={directorSupervisorForm.shPosCode}
                                                                    onChange={(e) => handleChange('directorSupervisorForm.shPosCode', e.target.value)}
                                                                    options={ifCfm ? shPosCodeSelectCfm : ifEn ? directorSupervisorForm.shPosCodeMap : directorSupervisorForm.shPosCodeMap}
                                                                />
                                                                <div className="mb-3">
                                                                    <Button primary onClick={() => tableAdd(directorSupervisorForm.shPosCode || '01')}>
                                                                        <Trans i18nKey={`${__group}.btn.add`}>增加</Trans>
                                                                    </Button>
                                                                </div>
                                                            </> : <>
                                                                {
                                                                    isCmpyLtd ? <>
                                                                        <Input type="select" value={directorSupervisorForm.shPosCode}
                                                                            onChange={(e) => handleChange('directorSupervisorForm.shPosCode', e.target.value)}
                                                                            // options={is401 ? 
                                                                            //     (ifEn ? shPosCodeSelect1En : shPosCodeSelect1) : 
                                                                            //     (ifEn ? shPosCodeSelect2En : shPosCodeSelect2)}
                                                                            options={directorSupervisorForm.shPosCodeMap}
                                                                        />
                                                                        <div className="mb-3">
                                                                            <Button primary onClick={() => tableAdd(directorSupervisorForm.shPosCode)}>
                                                                                <Trans i18nKey={`${__group}.btn.add`}>增加</Trans>
                                                                            </Button>
                                                                        </div>
                                                                    </> : <>
                                                                        {
                                                                            (is402 || is102) ? <>
                                                                                <Input type="select" value={directorSupervisorForm.shPosCode}
                                                                                    onChange={(e) => handleChange('directorSupervisorForm.shPosCode', e.target.value)}
                                                                                    options={ifEn ? directorSupervisorForm.shPosCodeMap : directorSupervisorForm.shPosCodeMap} />
                                                                                <div className="mb-3">
                                                                                    <Button primary onClick={() => tableAdd(directorSupervisorForm.shPosCode)}>
                                                                                        <Trans i18nKey={`${__group}.btn.add`}>增加</Trans>
                                                                                    </Button>
                                                                                </div>
                                                                            </> : <>
                                                                                {
                                                                                    (is401 || is418 || is604 || is414) && <>
                                                                                        <Input type="select" value={directorSupervisorForm.shPosCode}
                                                                                            onChange={(e) => handleChange('directorSupervisorForm.shPosCode', e.target.value)}
                                                                                            options={directorSupervisorForm.shPosCodeMap}
                                                                                        />
                                                                                        <div className="mb-3">
                                                                                            <Button primary onClick={() => tableAdd(directorSupervisorForm.shPosCode)}>
                                                                                                <Trans i18nKey={`${__group}.btn.add`}>增加</Trans>
                                                                                            </Button>
                                                                                        </div>
                                                                                    </>
                                                                                }
                                                                            </>
                                                                        }
                                                                    </>
                                                                }
                                                            </>
                                                        }</>
                                                    }
                                                </div>
                                            </div>
                                            <div className="col-md-6 text_right">
                                                {ifOss && (<>
                                                    {/* reload sh */}
                                                    <Button primary onClick={() => dispatch(actions.reload(store, dispatch))}>
                                                        <Trans i18nKey={`${__group}.reloadSh`}>重新載入公司登記資料</Trans>
                                                    </Button>
                                                </>)}
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                                <tr>
                                    <th align="center" style={{width: '5%'}} rowSpan={2}><Trans i18nKey={`${__group}.sh.seq`}>編號</Trans></th>
                                    <th align="center" style={{width: '10%'}}><Trans i18nKey={`${__group}.sh.posName`}>職稱</Trans></th>
                                    <th align="center" style={{width: '35%'}}><Trans i18nKey={`${__group}.sh.name`}>姓名<br/>(或法人名稱)</Trans></th>
                                    <th align="center" style={{width: '20%'}}><Trans i18nKey={`${__group}.sh.idNo`}>身分證號<br/>(或法人統一編號)</Trans></th>
                                    <th align="center" style={{width: '20%'}}>
                                        {
                                            isCmpyLtd ? 
                                                <Trans i18nKey={`${__group}.sh.investAmt1`}>出資額(元)</Trans>
                                                :
                                                <Trans i18nKey={`${__group}.sh.investAmt2`}>持有股份(股)</Trans>
                                        }
                                    </th>
                                    <th align="center" style={{width: '10%'}}><Trans i18nKey={`${__group}.sh.shCorp.posName`}>法人代表</Trans></th>
                                </tr>
                                <tr>
                                    <th align="center" colSpan={5}><Trans i18nKey={`${__group}.sh.addr`}>住所或居所(或法人所在地)</Trans></th>
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    (directorSupervisorForm.shareholders || []).map((sh, idx) => {
                                        return (
                                            <Fragment key={`sh_${idx}`}>
                                                <tr>
                                                    <td rowSpan={2}>
                                                        {(ifCfm && ifRegChange) && (
                                                            <Tooltip title={<Trans i18nKey={`${__group}.hint.ifModify`}>變更時請打勾</Trans>} arrow>
                                                                <CheckBox
                                                                    id={`idx_${(idx + 1)}`}
                                                                    onChange={() => handleChange('directorSupervisorForm.changeItemArr.sh', 
                                                                        processCheckboxChange(directorSupervisorForm.changeItemArr.sh, String(idx + 1)))}
                                                                    checked={(directorSupervisorForm.changeItemArr.sh || []).indexOf(String(idx + 1)) > -1}
                                                                />
                                                            </Tooltip>
                                                        )}
                                                        {idx + 1}
                                                    </td>
                                                    {/* <td>{sh.posName}</td> */}
                                                    <td>{ifCfm ? shPosCodeSelectCfm[sh.shPosCode] : shPosCodeMap[sh.shPosCode] || ''}</td>
                                                    <td>
                                                        <div className="input-group">
                                                            <Input 
                                                                value={sh.name || ''} 
                                                                onChange={(e) => {
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.name`, e.target.value);
                                                                }} 
                                                                maxLength="150" validateResult={store.validateResult} 
                                                                msg={`directorSupervisorForm.shareholders.${idx}.name`} 
                                                                ifShowMsgText={true}
                                                                placeholder={t(`${__group}.sh.name`).replace('<br/>', '')} />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div className="input-group">
                                                            <Input 
                                                                value={sh.idNo || ''} 
                                                                onChange={(e) => {
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.idNo`, e.target.value);
                                                                }} 
                                                                maxLength="15" 
                                                                validateResult={store.validateResult} 
                                                                msg={`directorSupervisorForm.shareholders.${idx}.idNo`} 
                                                                ifShowMsgText={true}
                                                                placeholder={t(`${__group}.sh.idNo`).replace('<br/>', '')} 
                                                            />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div className="input-group">
                                                            <Input
                                                                type="currency"
                                                                value={sh.investAmt || 0}
                                                                onChange={(e) => {
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.investAmt`, e.target.value);
                                                                }}
                                                                maxLength="20"
                                                                validateResult={store.validateResult}
                                                                msg={`directorSupervisorForm.shareholders.${idx}.investAmt`} 
                                                                placeholder={isCmpyLtd? t(`${__group}.sh.investAmt1`) : t(`${__group}.sh.investAmt2`)}
                                                            />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div className="input-group">
                                                            <Input type="select" 
                                                                value={sh.shCorp}
                                                                onChange={(e) => {
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.shCorp`, e.target.value);
                                                                }}
                                                                options={ ifEn ? ynMapEn : ynMap } />
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td colSpan={4}>
                                                        <div className="input-group">
                                                            {/* * shZipCode: 郵遞區號
                                                            * shAreaCode: 住所或居所(或法人所在地)鄉鎮市區代碼
                                                            * shNeiborCode: 住所或居所(或法人所在地)所在地地址-鄰
                                                            * shAddr: 住所或居所(或法人所在地)地址
                                                            * shAddrComb: 住所或居所(或法人所在地)合併地址
                                                            * */}
                                                            <AddressDialog
                                                                zipcode={sh.shZipCode || ''}
                                                                areacode={sh.shAreaCode || ''}
                                                                neighbor={sh.shNeiborCode || ''}
                                                                address={sh.shAddr || ''}
                                                                onChange={(e) => {
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.shZipCode`, e.target.value.zipcode);
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.shAreaCode`, e.target.value.areacode);
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.shNeiborCode`, e.target.value.neighbor);
                                                                    handleChange(`directorSupervisorForm.shareholders.${idx}.shAddr`, e.target.value.address);
                                                                }}
                                                                validateResult={store.validateResult}
                                                                msg={`directorSupervisorForm.shareholders.${idx}.shAreaCode`}
                                                                title={t(`${__group}.shAddrComb`)}
                                                                ifAllowForeignAddr={true}
                                                            />
                                                        </div>
                                                    </td>
                                                    <td>
                                                        {
                                                            isShowDel && (
                                                                <div className="input-group">
                                                                    <Button danger onClick={() => tableDelete(idx)}>
                                                                        <Trans i18nKey={`${__group}.btn.remove`}>刪除</Trans>
                                                                    </Button>
                                                                </div>
                                                            )
                                                        }
                                                    </td>
                                                </tr>
                                            </Fragment>
                                        );
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                    {ifOss && (
                        <CommonButton 
                            onPrevClick={() => dispatch(actions.prev(navigate))}
                            onRefillClick={() => dispatch(actions.refill())}
                            onSaveClick={() => dispatch(actions.save())}
                            onCompleteClick={() => dispatch(actions.complete(navigate))}
                            isVisible={!isViewMode}
                        />
                    )}
                    {ifCfm && (
                        <FormCommonButton
                            onSaveClick={() => dispatch(actions.save())}
                            onDownloadClick={() => dispatch(actions.download(ifRegChange))}
                            onGoToExampleClick={() => dispatch(actions.goToDownloadPage())}
                        />
                    )}
                </div>
            </div>
            {orgType == '01' && (<>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>1：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.1`}>填寫董事長請參照</Trans>
                    <a href="http://gcis.nat.gov.tw/elaw/lawDtlAction.do?method=lawToCons&pk=19&art=208&dash=0" target="_blank" rel="noreferrer" >
                        <Trans i18nKey={`${__group}.remarks.rule.1`}>《公司法》第208條第一項</Trans>
                    </a>
                    <Trans i18nKey={`${__group}.remarks.handling`}>辦理</Trans>
                </div>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>2：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.2`}>填寫董事請參照</Trans>
                    <a href="http://gcis.nat.gov.tw/elaw/lawDtlAction.do?method=lawToCons&pk=19&art=192&dash=0" target="_blank" rel="noreferrer" >
                        <Trans i18nKey={`${__group}.remarks.rule.2`}>《公司法》第192條第一項</Trans>
                    </a>
                    <Trans i18nKey={`${__group}.remarks.handling`}>辦理</Trans>
                </div>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>3：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.3`}>填寫董事長請參照</Trans>
                    <a href="http://gcis.nat.gov.tw/elaw/lawDtlAction.do?method=lawToCons&pk=19&art=216&dash=0" target="_blank" rel="noreferrer" >
                        <Trans i18nKey={`${__group}.remarks.rule.3`}>《公司法》第216條第一項</Trans>
                    </a>
                    <Trans i18nKey={`${__group}.remarks.handling`}>辦理</Trans>
                </div>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>4：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.5`}>外國法人若無統一編號，請輸入NA</Trans>
                </div>
            </>)}
            {orgType == '02' && (<>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>1：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.4`}>填寫董事長、董事、股東名單請參照</Trans>
                    <a href="http://gcis.nat.gov.tw/elaw/lawDtlAction.do?method=lawToCons&pk=19&art=108&dash=0" target="_blank" rel="noreferrer" >
                        <Trans i18nKey={`${__group}.remarks.rule.4`}>《公司法》第108條第一項</Trans>
                    </a>
                    <Trans i18nKey={`${__group}.remarks.handling`}>辦理</Trans>
                </div>
                <div className="label_notice">
                    <Trans i18nKey={`${__group}.remarks.title`}>註 </Trans>2：
                    <Trans i18nKey={`${__group}.remarks.shItemMemo.5`}>外國法人若無統一編號，請輸入NA</Trans>
                </div>
            </>)}
        </Fragment>
    );
};

const ComponentWithErrorBoundary = withErrorBoundary(Component, {
    fallback: <div>元件發生問題{__group}</div>,
    // onError(error, info) {
    // Do something with the error
    // E.g. log to an error logging client here
    // console.log(error);
    // console.log(info);
    // },
});

export default ComponentWithErrorBoundary;