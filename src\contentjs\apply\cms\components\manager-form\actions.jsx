/***************************************************
 * 說明: 路由元件 - 申請書: 經理人 - Actions
 * Copyright(c) 2024 AEB Inc. All Rights Reserved.
 ***************************************************/
import React from 'react';
import { Trans } from 'react-i18next';

import axios from 'js/http';
import * as reducer from 'js/reducers-util';
import LoadingSpinner from 'js/loading-spinner';
import { printError, appendAsJsonStrToFormData } from 'js/common-util';
import { validate, messegeObjectToOne } from 'js/validator-util';
import { SUCCESS, YES, NO } from 'js/constants';
import DownloadUtil from 'js/download-util';

import { awaitOpenDialog } from 'components/Dialog';
import { awaitOpenConfirm } from 'components/Confirm';
import { openSnackbar } from 'components/Snackbar';
import { SEC } from 'components/Header/SectionSwitch';
import { FOREIGN_ADDR_CODE } from 'components/InputField/AddressDialogField';

import { nextRouteByPathname as cmsNextRoute, prevRouteByPathname as cmsPrevRoute } from '../../actions';
import { default as practiceData } from './practice-data';

const loadingSpinner = new LoadingSpinner();
const downloadUtil = new DownloadUtil();

const setStateData = reducer.setStateData;
const deleteStateData = reducer.deleteStateData;
const toggleStateData = reducer.toggleStateData;

export { setStateData, deleteStateData, toggleStateData };

/** 群組名稱 */
const __group = 'manager-form';

/** 驗證規則: 存檔 */
export const validateRulesSave = {
    managerForm: {
        partnerId: {
            ifPersonId: { message: <Trans i18nKey={`${__group}.validate.partnerId`}>「經理人身分證號」格式錯誤</Trans> },
        },
    },
};

/** 驗證規則: 完成 */
export const validateRulesComplete = {
    managerForm: {
        ...validateRulesSave.managerForm,
    },
};

/**
 * 初始化
 */
export const init = () => {
    return async function (dispatch, getState) {
        loadingSpinner.show();
        const { telixNo, ifOss, ifCfm } = getState();
        return await axios
            .post(
                'init',
                {
                    telixNo: telixNo,
                    ifModify: sessionStorage.getItem('ifModify'),
                },
                {
                    headers: {
                        'content-type': 'multipart/form-data',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                }
            )
            .then(async (response) => {
                const { msg } = response.data;
                if (msg == SUCCESS) {
                    let responseJson;
                    let managers;
                    if (ifOss) {
                        responseJson = JSON.parse(response.data.data);
                        if (responseJson) {
                            dispatch(setStateData('managerForm', responseJson));
                            managers = responseJson.managers;
                        }
                        // 練習區展測資料
                        await dispatch(fillPracticeData());
                    } else if (ifCfm) {
                        const { mgrModel, applyType, changeItem } = response.data;
                        dispatch(setStateData('managerForm.managers', mgrModel));
                        managers = response.data.mgrModel;
                        dispatch(setStateData('managerForm.applyType', applyType));
                        if (changeItem) {
                            dispatch(setStateData('managerForm.changeItem', changeItem || ''));
                            const changeItemArr = JSON.parse(changeItem);
                            if (changeItemArr) {
                                dispatch(setStateData('managerForm.changeItemArr', changeItemArr));
                            }
                        }
                    }
                    (managers || []).map((manager, idx) => {
                        if (manager.commencementDate) {
                            const commencementDate = new Date(manager.commencementDate);
                            dispatch(setStateData(`managerForm.managers.${idx}.commencementDate`, commencementDate));
                        }
                    });
                }
            })
            .catch((err) => {
                handleException(err, dispatch);
            })
            .finally(() => {
                loadingSpinner.close();
            });
    };
};

/**
 * 重填
 */
export const refill = () => {
    return async function (dispatch, getState) {
        if (
            await dispatch(
                awaitOpenConfirm({
                    title: <Trans i18nKey={'confirm.refill.title'}>重填</Trans>,
                    content: <Trans i18nKey={'confirm.refill.content'}>表單資料將被清空，是否確定繼續?</Trans>,
                })
            )
        ) {
            const model = Object.assign({}, getState().managerForm);
            for (let k of Object.keys(model)) {
                if ('managers' == k) {
                    for (let m of Object.keys(model[k])) {
                        for (let n of Object.keys(model[k][m])) {
                            if (['telixNo', 'seqNo'].indexOf(n) < 0) {
                                model[k][m][n] = null;
                            }
                        }
                    }
                }
            }
            dispatch(setStateData('managerForm', model || {}));
        }
    };
};

/**
 * 存檔
 */
export const save = () => {
    return async function (dispatch, getState) {

        // 檢查所有經理人的身分證號碼
        const isIdValid = await dispatch(checkAllPartnerId());
        if (!isIdValid) return Promise.resolve(); 

        // const isChecked = await dispatch(checkSave());
        // if (isChecked) {
        // dispatch(setStateData('validateResult.managerForm', {}));
        loadingSpinner.show();

        const { ifOss, ifCfm, telixNo, managerForm, orgType } = getState();
        const { applyType } = managerForm;

        let url = '';
        let postObject = {};
        if (ifOss) {
            url = 'change-tab';
            const json = JSON.parse(JSON.stringify(managerForm));
            json.telixNo = getState().telixNo;
            if (Array.isArray(managerForm.managers) && managerForm.managers.length > 0) {
                json.ifComplete = (await dispatch(checkComplete(false))) ? YES : NO;
            } else {
                json.ifComplete = NO;
            }
            postObject = { jsonStr: JSON.stringify(json) };
        } else if (ifCfm) {
            url = 'save';
            postObject = appendAsJsonStrToFormData({
                telixNo,
                orgType,
                applyType,
                mgrModel: managerForm.managers,
                changeItem: JSON.stringify(managerForm.changeItemArr),
            });
        }
        return await axios
            .post(url, postObject, {
                headers: {
                    'content-type': 'multipart/form-data',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            })
            .then((response) => {
                const { msg, caseDetailList } = response.data;
                if (msg == SUCCESS) {
                    dispatch(openSnackbar(<Trans i18nKey={`${__group}.response.save`}>資料已存檔</Trans>));
                    dispatch(setStateData('caseDetailList', caseDetailList));
                    return Promise.resolve(SUCCESS);
                }
            })
            .catch((err) => {
                handleException(err, dispatch);
            })
            .finally(() => {
                loadingSpinner.close();
            });
    };
    // };
};

/**
 * 完成
 */
export const complete = (navigate) => {
    return async function (dispatch, getState) {
        const { managerForm } = getState();

        // 檢查經理人的身分證號碼
        const isIdValid = await dispatch(checkAllPartnerId());
        if (!isIdValid) return Promise.resolve(); 

        const isChecked = await dispatch(checkComplete(true));
        if (isChecked) {
            // dispatch(setStateData('validateResult.managerForm', {}));
            loadingSpinner.show();
            const json = JSON.parse(JSON.stringify(managerForm));
            json.telixNo = getState().telixNo;
            if (Array.isArray(managerForm.managers) && managerForm.managers.length > 0) {
                json.ifComplete = YES;
            } else {
                json.ifComplete = NO;
            }
            return await axios
                .post(
                    'next',
                    { jsonStr: JSON.stringify(json) },
                    {
                        headers: {
                            'content-type': 'multipart/form-data',
                            'X-Requested-With': 'XMLHttpRequest',
                        },
                    }
                )
                .then(async (response) => {
                    loadingSpinner.close();
                    const { msg, caseDetailList } = response.data;
                    if (msg == SUCCESS) {
                        dispatch(setStateData('caseDetailList', caseDetailList));
                        if (navigate) {
                            const pn = window.location.pathname;
                            dispatch(cmsNextRoute(pn)).then((url) => url && navigate(url));
                        }
                    }
                })
                .catch((err) => {
                    handleException(err, dispatch);
                })
                .finally(() => {
                    loadingSpinner.close();
                });
        }
    };
};

/**
 * 上一步
 */
export const prev = (navigate) => {
    return async function (dispatch, getState) {
        const { managerForm } = getState();
        
        // 檢查經理人的身分證號碼
        const isIdValid = await dispatch(checkAllPartnerId());
        if (!isIdValid) return Promise.resolve(); 

        // const isChecked = await dispatch(checkSave());
        // if (isChecked) {
        // dispatch(setStateData('validateResult.managerForm', {}));
        loadingSpinner.show();
        const json = JSON.parse(JSON.stringify(managerForm));
        json.telixNo = getState().telixNo;
        if (Array.isArray(managerForm.managers) && managerForm.managers.length > 0) {
            json.ifComplete = (await dispatch(checkComplete(false))) ? YES : NO;
        } else {
            json.ifComplete = NO;
        }
        return await axios
            .post(
                'change-tab',
                { jsonStr: JSON.stringify(json) },
                {
                    headers: {
                        'content-type': 'multipart/form-data',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                }
            )
            .then(async (response) => {
                const { msg, caseDetailList } = response.data;
                if (msg == SUCCESS) {
                    dispatch(openSnackbar(<Trans i18nKey={`${__group}.response.save`}>資料已存檔</Trans>));
                    dispatch(setStateData('caseDetailList', caseDetailList));
                    if (navigate) {
                        const pn = window.location.pathname;
                        dispatch(cmsPrevRoute(pn)).then((url) => url && navigate(url));
                    }
                }
            })
            .catch((err) => {
                handleException(err, dispatch);
            })
            .finally(() => {
                loadingSpinner.close();
            });
    };
    // };
};

export const download = () => {
    return async function (dispatch, getState) {
        // if (checkSave()) {
        dispatch(setStateData('validateResult.basicInfoForm', {}));
        loadingSpinner.show();

        const { telixNo, orgType, managerForm } = getState();
        const { applyType } = managerForm;
        let postObject = appendAsJsonStrToFormData({ telixNo, orgType, applyType, mgrModel: managerForm.managers });
        return await axios
            .post('save', postObject, {
                headers: {
                    'content-type': 'multipart/form-data',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            })
            .then(async (response) => {
                const { msg } = response.data;
                if (msg == SUCCESS) {
                    dispatch(openSnackbar(<Trans i18nKey={`${__group}.response.save`}>資料已存檔</Trans>));
                    dispatch(downloadPdf('download', telixNo));
                }
            })
            .catch((err) => {
                handleException(err, dispatch);
            })
            .finally(() => {
                loadingSpinner.close();
            });
    };
    // };
};

export const goToDownloadPage = () => {
    return function (dispatch, getState) {
        if (getState().orgType == '01') {
            window.open('https://gcis.nat.gov.tw/mainNew/subclassNAction.do?method=getFile&pk=48', 'popupWindow', 'location=0');
        } else if (getState().orgType == '02') {
            window.open('https://gcis.nat.gov.tw/mainNew/subclassNAction.do?method=getFile&pk=49', 'popupWindow', 'location=0');
        } else {
            window.open('https://gcis.nat.gov.tw/mainNew/subclassNAction.do?method=getFile&pk=41', 'popupWindow', 'location=0');
        }
    };
};

// const checkSave = () => {
//     return async function (dispatch, getState) {
//         const model = getState().managerForm;
//         const rslt = {};
//         model.managers.forEach((manager, idx) => {
//             const managerRules = {
//                 partnerId: {
//                     ifPersonId: {
//                         message: (
//                             <>
//                                 <Trans i18nKey={`${__group}.validate.number`}></Trans>
//                                 {idx + 1}
//                                 <Trans i18nKey={`${__group}.validate.partnerId`}></Trans>
//                             </>
//                         ),
//                     },
//                 },
//             };
//             const managerResult = validate(manager, managerRules) || {};
//             if (managerResult.partnerId) {
//                 rslt[`managers[${idx}.partnerId]`] = managerResult.partnerId;
//             }
//         });
//         if (Object.keys(rslt).length > 0) {
//             dispatch(setStateData('validateResult.managerForm', rslt || {}));
//             const msg = messegeObjectToOne(rslt, '\n');
//             if (msg) {
//                 dispatch(awaitOpenDialog({ content: msg }));
//             }
//             return false;
//         } else {
//             return true;
//         }
//     };
// };

const checkComplete = (tag) => {
    return async function (dispatch, getState) {
        const model = getState().managerForm;
        const rslt = {
            managerForm: {
                managers: []
            }
        };
        model.managers.forEach((manager, idx) => {
            const managerRules = {
                partnerName: {
                    presence: {
                        allowEmpty: false,
                        message: (
                            <>
                                <Trans i18nKey={`${__group}.validate.number`}></Trans>
                                {idx + 1}
                                <Trans i18nKey={`${__group}.presence.partnerName`}></Trans>
                            </>
                        ),
                    },
                },
                partnerId: {
                    presence: {
                        allowEmpty: false,
                        message: (
                            <>
                                <Trans i18nKey={`${__group}.validate.number`}></Trans>
                                {idx + 1}
                                <Trans i18nKey={`${__group}.presence.partnerId`}></Trans>
                            </>
                        ),
                    },
                },
                commencementDate: {
                    presence: {
                        allowEmpty: false,
                        message: (
                            <>
                                <Trans i18nKey={`${__group}.validate.number`}></Trans>
                                {idx + 1}
                                <Trans i18nKey={`${__group}.presence.commencementDate`}></Trans>
                            </>
                        ),
                    },
                },
                partnerAddr: {
                    presence: {
                        allowEmpty: false,
                        message: (
                            <>
                                <Trans i18nKey={`${__group}.validate.number`}></Trans>
                                {idx + 1}
                                <Trans i18nKey={`${__group}.presence.partnerAddr`}></Trans>
                            </>
                        ),
                    },
                },
            };
            const managerResult = validate(manager, managerRules) || {};
            if (Object.keys(managerResult).length > 0) {
                rslt.managerForm.managers[idx] = rslt.managerForm.managers[idx] || {};
                for (const key in managerResult) {
                    rslt.managerForm.managers[idx][key] = managerResult[key];
                }
            }
        });
        dispatch(setStateData('validateResult', rslt));

        const hasErrors = rslt.managerForm.managers.some(item => item && Object.keys(item).length > 0);
        console.log('validateResult=', rslt);
        if (hasErrors) {
            if (tag) {
                const flatErrors = {};
                (model.managers || []).forEach((_, idx) => {
                    const managersErrors = rslt.managerForm.managers[idx];
                    if (managersErrors) {
                        Object.keys(managersErrors).forEach(field => {
                            flatErrors[`managerForm.managers.${idx}.${field}`] = managersErrors[field];
                        });
                    }
                });
                const msg = messegeObjectToOne(flatErrors, '\n');
                dispatch(awaitOpenDialog({ content: msg }));
            }
            return false;
        } else {
            dispatch(setStateData('validateResult', {}));
            return true;
        }
    };
};

/**
 * 下載PDF共用元件
 * @param {*} url
 * @param {*} telixNo
 * @param {*} dispatch
 * @returns
 */
const downloadPdf = (url, telixNo) => {
    return async function (dispatch) {
        return await axios
            .post(
                url,
                {
                    telixNo: telixNo,
                    fileType: 'pdf',
                },
                { responseType: 'arraybuffer' },
                {
                    headers: {
                        'content-type': 'multipart/form-data',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                }
            )
            .then((response) => {
                let fileName = 'file';
                if (response.headers && response.headers['content-disposition']) {
                    const prefix = 'filename=';
                    let cd = response.headers['content-disposition'];
                    fileName = cd.substring(cd.indexOf(prefix) + prefix.length);
                }
                downloadUtil.downloadFile(response.data, response.data.type, fileName);
            })
            .catch((err) => {
                handleException(err, dispatch);
            })
            .finally(() => {
                //
            });
    };
};

/**
 * 填入練習區展測資料
 */
export const fillPracticeData = () => {
    return async function (dispatch, getState) {
        // 位於練習區
        if (getState().__sec == SEC.PRACTICE) {
            // 產生一份表單
            const managerForm = Object.assign({}, getState().managerForm);
            // 以下欄位略過不清空
            const ig = [''];
            // 判斷陣列
            const ar = ['managers'];
            // 填入值
            for (let [k, v] of Object.entries(managerForm)) {
                if (ig.indexOf(k) === -1) {
                    if (ar.indexOf(k) > -1) {
                        managerForm[k] = (Array.isArray(v) && v.length === 0) || v === undefined || v === null ? practiceData[k] : v;
                    } else {
                        managerForm[k] = v || practiceData[k];
                    }
                }
            }
            // 填入store
            dispatch(setStateData('managerForm', managerForm));
        }
    };
};

const handleException = (err, dispatch) => {
    if (err) {
        printError(err);
        if (err.response && err.response.data) {
            if (err.response.data.errorMsg == 'DATA_NOT_FOUND') {
                dispatch(openSnackbar(<Trans i18nKey={`${__group}.response.data-not-found`}>查無資料</Trans>));
            } else {
                const errorCode = err.response.data.errorMsg;
                dispatch(
                    awaitOpenDialog({
                        content: <Trans i18nKey={`error-code.${errorCode}`} defaults={<Trans i18nKey='error-code.ER0000' />} />,
                    })
                );
            }
        }
    }
};

/**
 * 檢查經理人的身分證號碼
 */
export const checkAllPartnerId = () => {
    return async function (dispatch, getState) {
        const { managers } = getState().managerForm;
        let isAllValid = true;
        const validateResult = {
            managerForm: {
                managers: []
            }
        };

        for (let i = 0; i < managers.length; i++) {
            const { partnerId, partnerName } = managers[i];
            if (!partnerId) continue;

            const rslt = validate({ partnerId: partnerId }, {
                partnerId: {
                    ifPersonId: {
                        message: <Trans i18nKey={`${__group}.validate.applyIdFormat`}>「身分證字號格式錯誤」</Trans>,
                    },
                },
            });

            if (rslt) {
                const confirmed = await dispatch(
                    awaitOpenConfirm({
                        content: (
                            <Trans
                                i18nKey={`${__group}.confirm.partnerId.content`}
                                values={{
                                    name: partnerName || `第 ${i + 1} 筆`,
                                    id: partnerId,
                                }}
                                defaults={'「{{name}}」身分證件號碼「{{id}}」是否為護照號碼？<br/>如有錯誤請按「重填」修改，如無錯誤請按「正確」繼續。'}
                            />
                        ),
                        okTxt: <Trans i18nKey={`${__group}.confirm.partnerId.correct`}>正確</Trans>,
                        noTxt: <Trans i18nKey={`${__group}.confirm.partnerId.refill`}>重填</Trans>,
                    })
                );

                if (!confirmed) {
                    if (!validateResult.managerForm.managers[i]) {
                        validateResult.managerForm.managers[i] = {};
                    }
                    validateResult.managerForm.managers[i].partnerId = rslt.partnerId;
                    isAllValid = false;
                    dispatch(setStateData(`managerForm.managers.${i}.partnerId`, ''));
                }
            }
        }

        if (!isAllValid) {
            dispatch(setStateData('validateResult', validateResult));
            console.log('印出來看validateResult=',validateResult);
        }

        return isAllValid;
    };
};